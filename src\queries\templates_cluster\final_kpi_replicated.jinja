{# Template for creating replicated table for final KPI data in cluster mode #}
CREATE TABLE IF NOT EXISTS {{ cluster_database }}.final_kpi_{{ table_suffix }}_replicated ON CLUSTER {{ cluster_name }}
(
    {% if labels %}
        {% for axis_key, axis_data in axes.items() %}
            {% if axis_data["labels"] is not none and axis_data["type"] is not none %}
                {{ axis_key }}_{{ axis_data["name"] }} String,
            {% endif %}
            {% if position_numbers %}
                {{ axis_data["name"] }}_position_number UInt16,
            {% endif %}
        {% endfor %}
    {% endif %}
    period_name String,
    Fact String,
    Value Float64
)
ENGINE = ReplicatedMergeTree('/clickhouse/tables/{shard}/final_kpi_{{ table_suffix }}_replicated', '{replica}')
ORDER BY (period_name, Fact)
SETTINGS index_granularity = 8192
