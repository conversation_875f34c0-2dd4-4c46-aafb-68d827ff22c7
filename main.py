"""
Main entry point for the KPI application using the service-based architecture.

This module provides the main entry point for the KPI application.
It handles command-line arguments, sets up logging, and processes jobs.
"""

import logging
import sys
import traceback
import time

from src.core.config import config
from src.core.app import initialize_app
from src.core.connection_manager import connection_manager
from src.core.exceptions import ConfigurationError, QueryError
from src.models.kpi import KPIError
from src.services.job_service import JobService
from src.utils.error_handling import log_exception
from src.utils.formatting import format_duration
from src.utils.clickhouse_error_parser import (
    parse_clickhouse_error,
    get_simplified_error_message,
)
from src.utils.job_api_client import set_job_progress_message, set_job_status


def parse_command_line_args():
    """Parse command line arguments."""
    # We expect command-line parameters: id_job username output_dir [--cluster-mode]
    if len(sys.argv) < 2:
        error_message = "KPI: command-line parameter with id_job is not provided!"
        logging.error(error_message)
        raise ConfigurationError(error_message)

    try:
        id_job = int(sys.argv[1])
    except ValueError:
        error_message = f"KPI: id_job must be an integer, got {sys.argv[1]}"
        logging.error(error_message)
        raise ConfigurationError(error_message)

    username = ""
    if len(sys.argv) >= 3:
        username = str(sys.argv[2])

    output_dir = config.output_dir
    if len(sys.argv) >= 4:
        output_dir = str(sys.argv[3])

    # Check for cluster mode flag
    cluster_mode = False
    if "--cluster-mode" in sys.argv:
        cluster_mode = True
        logging.info("Cluster mode enabled via command line argument")

    return id_job, username, output_dir, cluster_mode


def process_job(job_id: int, username: str, cluster_mode: bool = False):
    """Process a KPI job.

    This function processes a KPI job and sets the job status to "done" if successful
    or "error" if there are errors. The main script may override this status if needed.

    Args:
        job_id: The job ID to process
        username: The username running the job
        cluster_mode: Whether to use cluster-based processing

    Returns:
        KPIResult model with result information
    """
    # Start timing for total execution
    start_time = time.time()

    # Define simplified message logger function for UI logging
    def msg_logger_func(msg):
        # Get the UI logger instead of using root logger
        ui_logger = logging.getLogger("ui")
        ui_logger.info(f"Job {job_id}: {msg}")
        # Note: Progress tracking is now handled by the integrated ProgressTracker in JobService

    # Update job progress - initial status
    set_job_progress_message(job_id, "Initializing job service", 0)

    # Create the job service
    job_service = JobService(
        connection=connection_manager.get_clickhouse_connection(),
        msg_logger_func=msg_logger_func,
    )

    # Process the job
    result = job_service.process_job(job_id, username)

    # Log errors to job
    if result.errors:
        # Handle both string errors and KPIError objects
        error_messages = []
        for error in result.errors:
            if isinstance(error, KPIError):
                # Format simplified error information
                error_code_str = (
                    f" (Code: {error.error_code})" if error.error_code else ""
                )
                error_period_str = f" in period {error.period}" if error.period else ""

                error_msg = f"{error.message}{error_code_str}{error_period_str}"
                error_messages.append(error_msg)

                # Log the detailed error separately
                if error.error_code:
                    logging.error(
                        f"ClickHouse error code {error.error_code}: {error.message}"
                    )
            else:
                # Simple string error
                error_messages.append(str(error))

        logging.error(f"Job {job_id} errors: {', '.join(error_messages)}")

    # Log result IDs
    if result.result_ids:
        logging.info(
            f"Job {job_id}: Results stored in ClickHouse with IDs: {', '.join(result.result_ids)}"
        )
    else:
        logging.info(f"Job {job_id}: No results were generated")

    # Job is complete - indicate if there were errors
    if result.errors:
        logging.warning(
            f"Job {job_id}: Processing complete with errors - {len(result.result_ids)} result(s) generated but job has errors"
        )
    else:
        logging.info(
            f"Job {job_id}: Processing complete - {len(result.result_ids)} result(s) generated"
        )

    # Calculate and log total execution time
    duration = format_duration((time.time() - start_time) * 1000)
    logging.info(f"Job {job_id}: Total execution time: {duration}")

    set_job_progress_message(job_id, f"Job completed in {duration}")

    # Set job status to "done" if there are no errors, otherwise "error"
    try:
        if result.errors:
            # Format error messages
            error_messages = []
            for error in result.errors:
                if isinstance(error, KPIError):
                    # Format simplified error information
                    error_code_str = (
                        f" (Code: {error.error_code})" if error.error_code else ""
                    )
                    error_period_str = (
                        f" in period {error.period}" if error.period else ""
                    )
                    error_msg = f"{error.message}{error_code_str}{error_period_str}"
                    error_messages.append(error_msg)
                else:
                    # Simple string error
                    error_messages.append(str(error))

            # Join all errors into a single message
            error_message = "; ".join(error_messages)
            set_job_status(job_id, "error", error_message)
            logging.info(
                f"Job {job_id}: Status set to 'error' with message: {error_message}"
            )
        else:
            set_job_status(job_id, "done")
            logging.info(f"Job {job_id}: Status set to 'done'")
    except Exception as e:
        logging.warning(f"Failed to update final job status: {e}")
        # Continue even if status update fails

    return result


if __name__ == "__main__":
    try:
        # Parse command line arguments
        id_job, username, output_dir, cluster_mode = parse_command_line_args()

        # Initialize the application
        initialize_app(username=username, output_dir=output_dir)

        # Set job status to "inprogress" when starting
        try:
            set_job_status(id_job, "inprogress")
            logging.info(f"Job {id_job}: Status set to 'inprogress'")
            set_job_progress_message(id_job, "Job processing started")
        except Exception as e:
            logging.warning(f"Failed to update initial job status to 'inprogress': {e}")
            # Continue even if status update fails

        # Process job
        try:
            logging.info(f"[{'=' * 40} Starting job_id={id_job} {'=' * 40}]")
            result = process_job(id_job, username, cluster_mode)

            # Check if there were any errors
            if result.errors:
                # Format the first error for display
                if isinstance(result.errors[0], KPIError):
                    error = result.errors[0]
                    error_code_str = (
                        f" (Code: {error.error_code})" if error.error_code else ""
                    )
                    period_str = f" in period {error.period}" if error.period else ""
                    error_message = f"{error.message}{error_code_str}{period_str}"
                else:
                    error_message = str(result.errors[0])

                # Log the error status
                logging.error(f"Job job_id={id_job} failed: {error_message}")

                # If there are more errors, log them too
                if len(result.errors) > 1:
                    logging.error(
                        f"Job job_id={id_job} had {len(result.errors)} total errors"
                    )

                # Check if we have result IDs even though there were errors
                if result.result_ids:
                    logging.error(
                        f"Job job_id={id_job} completed with errors - results stored in ClickHouse with ID(s): {', '.join(result.result_ids)} but job marked as failed"
                    )
                else:
                    logging.error(
                        f"Job job_id={id_job} failed with errors - no results were stored in ClickHouse"
                    )

                # Note: The job status might already be set to "error" in the process_job function,
                # but we set it again here to ensure the correct error message is used
                try:
                    set_job_status(id_job, "error", error_message)
                    logging.info(
                        f"Job {id_job}: Status set to 'error' with message: {error_message}"
                    )
                except Exception as status_error:
                    logging.warning(
                        f"Failed to update job status to 'error': {status_error}"
                    )

                # Exit with error code
                sys.exit(1)
            else:
                # No errors, job completed successfully
                logging.info(f"Job job_id={id_job} completed successfully")

                # Note: Job status is already set to "done" in the process_job function
        except QueryError as e:
            # Handle ClickHouse errors specifically
            # Get simplified error message
            error_code = getattr(e, "error_code", None)
            simplified_message = get_simplified_error_message(str(e), error_code)

            error_code_str = f" (Code: {error_code})" if error_code else ""
            error_message = f"{simplified_message}{error_code_str}"

            # Log the error with full details
            log_exception(logging.getLogger(__name__), e, error_message)

            # Log error status
            logging.error(
                f"Job job_id={id_job} failed due to ClickHouse error{error_code_str}"
            )

            # Exit with error code
            sys.exit(1)
        except Exception as e:
            # Check if it's a ClickHouse error that wasn't caught as QueryError
            error_info = parse_clickhouse_error(str(e))
            if error_info["error_code"] is not None:
                # It's a ClickHouse error - get simplified message
                error_code = error_info["error_code"]
                simplified_message = get_simplified_error_message(str(e), error_code)
                error_message = f"{simplified_message} (Code: {error_code})"

                # Log the error with full details
                log_exception(logging.getLogger(__name__), e, error_message)

                # Log error status
                logging.error(
                    f"Job job_id={id_job} failed due to ClickHouse error (Code: {error_code})"
                )
            else:
                # Regular error
                error_message = f"Error processing job: {e}"
                log_exception(logging.getLogger(__name__), e, error_message)

                # Log error status
                logging.error(f"Job job_id={id_job} failed: {error_message}")

            # Set job status to "error" for unhandled exceptions
            try:
                set_job_status(id_job, "error", str(error_message))
                logging.info(
                    f"Job {id_job}: Status set to 'error' due to unhandled exception"
                )
            except Exception as status_error:
                logging.warning(
                    f"Failed to update job status to 'error': {status_error}"
                )

            # Exit with error code
            sys.exit(1)

    except Exception as e:
        # Log the error
        error_message = f"Critical error: {e}"
        print(error_message)
        traceback.print_exc()

        # Log to file if possible
        try:
            logging.error(f"Critical error in main process: {error_message}")
        except Exception as log_error:
            print(f"Failed to log error: {log_error}")

        # Exit with error code
        sys.exit(2)
    finally:
        # Close the connection manager
        connection_manager.close()
