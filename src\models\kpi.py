from enum import Enum
from typing import Any, Dict, List, Optional, Union
from pydantic import Field

from src.models.axis import (
    DictLikeModel,
    AxisData,
    FilterData,
    Period,
    FactsData,
    SUFactData,
)


class KPIType(str, Enum):
    """Enum for KPI types."""

    STANDARD_KPI = "standard_kpi"
    CATMAN_KPI = "catman_kpi"


class KPIRequest(DictLikeModel):
    """Model for KPI request data."""

    periods: List[Dict]
    axes: Dict
    filters: Dict
    kpi_type: KPIType
    id_panel: int
    analysis_name: str
    facts_axis: Optional[List[Dict]] = None
    su_fact: Optional[str] = None
    required_facts: Optional[List[str]] = None
    product_group: Optional[List[str]] = None


class KPIError(DictLikeModel):
    """Model for detailed KPI error information."""

    message: str
    error_code: Optional[int] = None
    period: Optional[str] = None


class KPIResult(DictLikeModel):
    """Model for KPI processing result."""

    exported_tables: List[str] = Field(default_factory=list)
    errors: List[Union[str, KPIError]] = Field(default_factory=list)
    result_ids: List[str] = Field(default_factory=list)

    def add_error(
        self,
        error: Union[str, Exception],
        error_code: Optional[int] = None,
        period: Optional[str] = None,
    ) -> None:
        """
        Add an error to the result with simplified information.

        Args:
            error: Error message or exception
            error_code: Optional error code (e.g., ClickHouse error code)
            period: Optional period name where the error occurred
        """
        if isinstance(error, Exception):
            message = str(error)
        else:
            message = error

        self.errors.append(
            KPIError(
                message=message,
                error_code=error_code,
                period=period,
            )
        )


class JobParameters(DictLikeModel):
    """Model for job parameters."""

    final_query: str = Field(None, description="Final query text")
    query_settings: Optional[Dict[str, Any]] = Field(None, description="Query settings")
    job_id: str = Field(default="unknown", description="Unique identifier for the job")
    username: str = Field(default="unknown", description="Username")
    analysis_name: str = Field(default="unknown", description="Name of the analysis")
    periods: Union[dict, List[Period]] = Field(
        ..., description="List of Period models or period dictionary"
    )
    period: Optional[Period] = None
    kpi_type: KPIType = Field(..., description="Type of KPI")
    id_panel: int = Field(1, description="Panel ID")
    product_group: Optional[List[str]] = Field(
        None, description="Product group or Market"
    )
    axes: Union[Dict, Dict[str, AxisData]] = Field(
        default_factory=dict, description="Axes"
    )
    filters: Union[Dict, Dict[str, FilterData]] = Field(
        default_factory=dict, description="Filters"
    )
    facts_axis: Optional[Union[List[Dict], List[FactsData]]] = Field(
        default_factory=list, description="Facts data (measures)"
    )
    required_facts: Optional[List[str]] = Field(
        default_factory=list, description="Required base facts for KPI calculation"
    )
    su_fact_data: Optional[Union[str, SUFactData]] = Field(
        None, description="SU fact data"
    )
    combined_result_id: Optional[str] = Field(None, description="Combined result ID")
    query_steps: Optional[str] = Field(None, description="Query steps")
    query_ids: Optional[List[str]] = Field([], description="List of all Query IDs")
    result_rows: Optional[int] = Field(
        0, description="Number of rows in the result table"
    )
    job_duration: Optional[float] = Field(None, description="Job duration")
    errors: Optional[List[Any]] = Field(
        None, description="List of all errors encountered during job execution"
    )
    final_result_table: Optional[str] = Field(
        None, description="Name of the final result table created during job processing"
    )

    def update(
        self,
        axes: Optional[Union[Dict, Dict[str, AxisData]]] = None,
        filters: Optional[Union[Dict, Dict[str, FilterData]]] = None,
        periods: Optional[Union[dict, List[Period]]] = None,
        period: Optional[Period] = None,
        **kwargs: Any,
    ) -> None:
        """
        Update job parameters with type conversion handling.

        Args:
            axes: New axes configuration
            filters: New filters configuration
            periods: New periods configuration
            period: Single period configuration
            **kwargs: Additional parameters to update
        """
        if axes is not None:
            if isinstance(axes, dict) and not all(
                isinstance(v, AxisData) for v in axes.values()
            ):
                # Convert plain dict to Dict[str, AxisData]
                self.axes = {
                    k: AxisData(**v) if not isinstance(v, AxisData) else v
                    for k, v in axes.items()
                }
            else:
                self.axes = axes

        if filters is not None:
            if isinstance(filters, dict) and not all(
                isinstance(v, FilterData) for v in filters.values()
            ):
                # Convert plain dict to Dict[str, FilterData]
                self.filters = {
                    k: FilterData(**v) if not isinstance(v, FilterData) else v
                    for k, v in filters.items()
                }
            else:
                self.filters = filters

        if periods is not None:
            if isinstance(periods, dict):
                self.periods = [Period(**periods)]
            elif isinstance(periods, list) and periods and isinstance(periods[0], dict):
                self.periods = [Period(**p) for p in periods]
            else:
                self.periods = periods

        if period is not None:
            if isinstance(period, dict):
                self.period = Period(**period)
            else:
                self.period = period

        # Update any additional parameters
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def extend(
        self,
        **kwargs: Any,
    ) -> None:
        """
        Extend job parameters by merging new values with existing ones.

        Args:
            **kwargs: Additional parameters to extend
        """
        # Extend additional parameters
        for key, value in kwargs.items():
            if hasattr(self, key):
                current_value = getattr(self, key)
                if isinstance(current_value, list):
                    if isinstance(value, list):
                        current_value.extend(value)
                    else:
                        current_value.append(value)
                elif isinstance(current_value, dict):
                    if isinstance(value, dict):
                        current_value.update(value)
                else:
                    setattr(self, key, value)
