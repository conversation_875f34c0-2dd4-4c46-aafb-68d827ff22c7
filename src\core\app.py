"""
Application initialization and setup.

This module contains functions for initializing the application,
setting up the dependency injection container, and configuring logging.
"""

import logging
import os
from typing import Optional

from src.core.config import config


def setup_logging(
    username: str, memlog_folder: str = "memlog", level: int = logging.INFO
) -> None:
    """
    Set up logging for the application.

    Args:
        username: The username running the job
        memlog_folder: The folder for storing memory logs
        level: The logging level to use
    """
    try:
        # Convert to absolute path if it's a relative path
        if not os.path.isabs(memlog_folder):
            memlog_folder = os.path.abspath(memlog_folder)
            print(f"Using absolute memlog path: {memlog_folder}")

        # Ensure the log directory exists
        if not os.path.exists(memlog_folder):
            print(f"Creating memlog directory: {memlog_folder}")
            os.makedirs(memlog_folder, exist_ok=True)

        # Create the log file path
        log_file_path = os.path.join(memlog_folder, f"kpi_{username}.log")
        print(f"Setting up logging to file: {log_file_path}")

        # Create file handler separately to catch any errors
        try:
            file_handler = logging.FileHandler(log_file_path)
            print(f"Successfully created file handler for: {log_file_path}")
        except Exception as e:
            print(f"ERROR creating file handler: {str(e)}")
            # Fall back to just console logging if file handler fails
            logging.basicConfig(
                level=level,
                format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                handlers=[logging.StreamHandler()],  # Console handler only
            )
            print("Falling back to console-only logging")
            return

        # Configure root logger with both handlers
        logging.basicConfig(
            level=level,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            handlers=[
                logging.StreamHandler(),  # Console handler
                file_handler,  # File handler
            ],
        )

        # Verify log file was created
        if os.path.exists(log_file_path):
            print(f"Log file created successfully: {log_file_path}")
        else:
            print(f"WARNING: Log file was not created at: {log_file_path}")

        # Create a UI logger that doesn't propagate to root
        ui_logger = logging.getLogger("ui")
        ui_logger.propagate = False  # Don't send to root logger (file/console)
        ui_logger.setLevel(level)

        # Set specific loggers to different levels if needed
        logging.getLogger("urllib3").setLevel(logging.WARNING)
        logging.getLogger("httpx").setLevel(logging.WARNING)

    except Exception as e:
        # If anything goes wrong, fall back to basic console logging
        print(f"ERROR setting up logging: {str(e)}")
        logging.basicConfig(
            level=level,
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        )


def setup_output_directory(output_dir: Optional[str] = None) -> str:
    """
    Set up the output directory for the application.

    Args:
        output_dir: Optional output directory override

    Returns:
        The path to the output directory
    """
    # Use provided output directory or fall back to config
    output_dir = output_dir or config.output_dir

    # Create the output directory if it doesn't exist
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    return output_dir


def setup_memlog_directory() -> str:
    """
    Set up the memory log directory for the application.

    Returns:
        The path to the memory log directory
    """
    try:
        memlog_folder = config.memlog_folder

        # Create the memory log directory if it doesn't exist
        if not os.path.exists(memlog_folder):
            print(f"Creating memlog directory: {memlog_folder}")
            os.makedirs(memlog_folder, exist_ok=True)

        # Verify directory was created
        if os.path.exists(memlog_folder):
            print(f"Memlog directory exists: {memlog_folder}")
        else:
            print(f"WARNING: Failed to create memlog directory: {memlog_folder}")

        return memlog_folder

    except Exception as e:
        print(f"ERROR setting up memlog directory: {str(e)}")
        # Fall back to a local directory that should be writable
        fallback_dir = os.path.abspath("./logs")
        print(f"Falling back to local directory: {fallback_dir}")
        os.makedirs(fallback_dir, exist_ok=True)
        return fallback_dir


def initialize_app(
    username: Optional[str] = None,
    memlog_folder: Optional[str] = None,
    output_dir: Optional[str] = None,
    skip_connection_init: bool = False,
) -> None:
    """
    Initialize the application.

    Args:
        username: Username for logging purposes
        memlog_folder: Optional memory log directory override
        output_dir: Optional output directory override
        skip_connection_init: Whether to skip connection manager initialization
    """
    print(f"Initializing application with username: {username}")

    # Use a default username if none provided
    if username is None or username.strip() == "":
        username = "default_user"
        print(f"No username provided, using default: {username}")

    # Set up memlog directory first
    if memlog_folder is None:
        memlog_folder = setup_memlog_directory()
    else:
        print(f"Using provided memlog folder: {memlog_folder}")
        # Ensure it exists
        if not os.path.exists(memlog_folder):
            print(f"Creating provided memlog directory: {memlog_folder}")
            os.makedirs(memlog_folder, exist_ok=True)

    # Set up logging with the configured memlog directory
    print(
        f"Setting up logging with memlog folder: {memlog_folder} and username: {username}"
    )
    setup_logging(username=username, memlog_folder=memlog_folder)

    # Log that we've initialized logging
    logging.info(
        f"Logging initialized for user {username} in directory {memlog_folder}"
    )

    # Set up the output directory
    output_dir = setup_output_directory(output_dir)
    logging.info(f"Output directory set to {output_dir}")

    # Initialize the connection manager and register the ClickHouse connection
    if not skip_connection_init:
        logging.info("Initializing connection manager")
        from src.core.connection_manager import connection_manager

        connection_manager.initialize()
        logging.info("Connection manager initialized")
    else:
        logging.info("Skipping connection manager initialization (will be done later)")
