{# Template for inserting data into distributed final KPI table in cluster mode #}
INSERT INTO {{ cluster_database }}.final_kpi_{{ table_suffix }}_distributed
{% if facts_axis is not none and facts_axis|length > 0 %}
    {% for fact in facts_axis %}
        {% if not loop.first %} UNION ALL {% endif %}
        SELECT
        {% if labels %}
            {% for axis_key, axis_data in axes.items() %}
                {% if axis_data["labels"] is not none and axis_data["type"] is not none %}
                    {% if catman %}
                        {{ axis_key }}.value_group_name AS {{ axis_key }}_{{ axis_data["name"] }},
                    {% else %}
                        if(splitByChar('|', {{ axis_key }}_position_number)[2] = '', {{ axis_key }}.value_group_name, splitByChar('|', {{ axis_key }}_position_number)[2])  AS {{ axis_key }}_{{ axis_data["name"] }},
                    {% endif %}
                {% endif %}
                {% if position_numbers %}
                    {% if catman %}
                        {{ axis_key }}_position_number as {{ axis_data["name"] }}_position_number,
                    {% else %}
                        toUInt16(splitByChar('|', {{ axis_key }}_position_number)[1]) as {{ axis_data["name"] }}_position_number,
                    {% endif %}
                {% endif %}
            {% endfor %}
        {% endif %}
        '{{ period.label }}' AS period_name,
        '{{ fact.display_name }}' AS Fact,
        {{ fact.formula }}/{{ fact.divisor }} AS Value
        FROM {{ cluster_database }}.buyers_{{ table_suffix }}_distributed a
        {% if labels %}
            {% for axis_key, axis_data in axes.items() %}
                {% if axis_data["labels"] is not none and axis_data["type"] is not none %}
                    LEFT JOIN (
                    SELECT
                    position_number,
                    value_group_name
                    FROM
                    (
                    {% for label in axis_data["labels"] %}
                        SELECT
                        {{ label.position_number }} AS position_number,
                        '{{ label.value_group_name }}' AS value_group_name
                        {% if not loop.last %}
                            UNION ALL
                        {% endif %}
                    {% endfor %}
                    )
                    ) {{ axis_key }} ON 
                    {% if catman %}
                        {{ axis_key }}.position_number = {{ axis_key }}_position_number
                    {% else %}
                        toUInt16(splitByChar('|', {{ axis_key }}_position_number)[1]) = {{ axis_key }}.position_number
                    {% endif %}
                {% endif %}
            {% endfor %}
        {% endif %}
        ORDER BY
        {% for axis_key, axis_data in axes.items() %}
            {% if axis_data["type"] is not none %}
                {% if not loop.first %}, {% endif %}
                    {{ axis_key }}_position_number
                {% endif %}
        {% endfor %}
        {% if labels %}
            {% for axis_key, axis_data in axes.items() %}
                {% if axis_data["labels"] is not none and axis_data["type"] is not none %}
                    , {{ axis_key }}_{{ axis_data["name"] }}
                {% endif %}
            {% endfor %}
        {% endif %}
    {% endfor %}
{% endif %}
