"""
Connection Manager for the application.

This module provides a centralized manager for database connections.
It handles connection pooling, lifecycle management, and configuration.
"""

import logging
import asyncio
from typing import Dict, Any, Optional

from src.core.clickhouse_connect import ClickHouseConnection
from src.core.config import config


class ConnectionManager:
    """
    Manages database connections for the application.

    This class provides a centralized way to manage database connections,
    including connection pooling, lifecycle management, and configuration.
    """

    _instance = None

    def __new__(cls):
        """Singleton pattern implementation."""
        if cls._instance is None:
            cls._instance = super(ConnectionManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """Initialize the connection manager."""
        if getattr(self, "_initialized", False):
            return

        self.logger = logging.getLogger(self.__class__.__name__)
        self.clickhouse_connection = None
        self._initialized = True

    def initialize(self):
        """Initialize connections."""
        self.logger.info("Initializing database connections")
        self._initialize_clickhouse_connection()

    def _initialize_clickhouse_connection(self):
        """Initialize the ClickHouse connection."""
        if self.clickhouse_connection is not None:
            self.logger.info("ClickHouse connection already initialized")
            return

        self.logger.info("Initializing ClickHouse connection")
        self.clickhouse_connection = ClickHouseConnection(
            host=config.clickhouse_host,
            port=config.clickhouse_port,
            user=config.clickhouse_user,
            password=config.clickhouse_password,
            database=config.clickhouse_database,
        )

        if not self.clickhouse_connection.client:
            self.logger.error("Failed to initialize ClickHouse connection")
            raise ConnectionError("Failed to initialize ClickHouse connection")

        self.logger.info("ClickHouse connection initialized successfully")

    def get_clickhouse_connection(self) -> ClickHouseConnection:
        """
        Get the ClickHouse connection.

        Returns:
            ClickHouse connection

        Raises:
            ConnectionError: If the connection is not initialized
        """
        if self.clickhouse_connection is None:
            self.logger.error("ClickHouse connection not initialized")
            raise ConnectionError("ClickHouse connection not initialized")

        return self.clickhouse_connection

    def close(self):
        """Close all connections."""
        self.logger.info("Closing all database connections")

        if self.clickhouse_connection is not None:
            self.clickhouse_connection.close()
            self.clickhouse_connection = None
            self.logger.info("ClickHouse connection closed")


# Create a singleton instance
connection_manager = ConnectionManager()
