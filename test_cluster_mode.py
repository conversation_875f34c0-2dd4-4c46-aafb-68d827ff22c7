#!/usr/bin/env python3
"""
Test script to verify cluster mode functionality without requiring actual ClickHouse connections.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.queries.query_builder import ClickHouseQuery
from src.core.config import config

def test_cluster_mode_parsing():
    """Test that cluster mode is parsed correctly from command line arguments."""
    print("Testing cluster mode argument parsing...")
    
    # Simulate command line arguments
    original_argv = sys.argv.copy()
    
    try:
        # Test without cluster mode
        sys.argv = ["test_script.py", "123", "test_user", "/tmp/output"]
        from main import parse_command_line_args
        
        # Reload the module to get fresh parsing
        import importlib
        import main
        importlib.reload(main)
        
        id_job, username, output_dir, cluster_mode = main.parse_command_line_args()
        print(f"Without --cluster-mode: cluster_mode = {cluster_mode}")
        assert cluster_mode == False, "Cluster mode should be False when flag is not provided"
        
        # Test with cluster mode
        sys.argv = ["test_script.py", "123", "test_user", "/tmp/output", "--cluster-mode"]
        importlib.reload(main)
        
        id_job, username, output_dir, cluster_mode = main.parse_command_line_args()
        print(f"With --cluster-mode: cluster_mode = {cluster_mode}")
        assert cluster_mode == True, "Cluster mode should be True when flag is provided"
        
        print("✓ Cluster mode argument parsing works correctly!")
        
    finally:
        sys.argv = original_argv

def test_query_builder_cluster_mode():
    """Test that query builder supports cluster mode."""
    print("\nTesting query builder cluster mode...")
    
    # Test cluster configuration
    cluster_config = {
        "cluster_name": "test_cluster",
        "database": "test_db",
        "table_suffix": "test123"
    }
    
    # Create query builder with cluster mode
    query_builder = ClickHouseQuery(
        cluster_mode=True,
        cluster_config=cluster_config
    )
    
    print(f"Query builder cluster mode: {query_builder.cluster_mode}")
    print(f"Query builder cluster config: {query_builder.cluster_config}")
    
    assert query_builder.cluster_mode == True, "Query builder should be in cluster mode"
    assert query_builder.cluster_config == cluster_config, "Cluster config should be set correctly"
    
    print("✓ Query builder cluster mode works correctly!")

def test_config_cluster_settings():
    """Test that cluster configuration settings are available."""
    print("\nTesting cluster configuration settings...")
    
    # Test that cluster settings exist
    assert hasattr(config, 'clickhouse_cluster'), "Config should have clickhouse_cluster attribute"
    assert hasattr(config, 'clickhouse_cluster_host'), "Config should have clickhouse_cluster_host property"
    assert hasattr(config, 'clickhouse_cluster_port'), "Config should have clickhouse_cluster_port property"
    assert hasattr(config, 'clickhouse_cluster_name'), "Config should have clickhouse_cluster_name property"
    
    print(f"Cluster host: {config.clickhouse_cluster_host}")
    print(f"Cluster port: {config.clickhouse_cluster_port}")
    print(f"Cluster name: {config.clickhouse_cluster_name}")
    
    print("✓ Cluster configuration settings are available!")

def test_template_directory_selection():
    """Test that cluster mode selects the correct template directory."""
    print("\nTesting template directory selection...")
    
    # Test regular mode
    query_builder_regular = ClickHouseQuery(cluster_mode=False)
    print(f"Regular mode template dir: {query_builder_regular.template_manager.template_dir}")
    
    # Test cluster mode
    query_builder_cluster = ClickHouseQuery(cluster_mode=True)
    print(f"Cluster mode template dir: {query_builder_cluster.template_manager.template_dir}")
    
    assert "templates_cluster" in query_builder_cluster.template_manager.template_dir, \
        "Cluster mode should use templates_cluster directory"
    
    print("✓ Template directory selection works correctly!")

if __name__ == "__main__":
    print("Running cluster mode tests...\n")
    
    try:
        test_cluster_mode_parsing()
        test_query_builder_cluster_mode()
        test_config_cluster_settings()
        test_template_directory_selection()
        
        print("\n🎉 All cluster mode tests passed!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
